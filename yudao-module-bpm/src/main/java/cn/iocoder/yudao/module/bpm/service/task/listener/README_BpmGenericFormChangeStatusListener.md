# BpmGenericFormChangeStatusListener 使用说明

## 概述

`BpmGenericFormChangeStatusListener` 是原 `BpmGenericFormChangeListener` 的正确版本，解决了原监听器的时序问题。

### 问题背景

原 `BpmGenericFormChangeListener` 使用 `ExecutionListener` 监听 `end` 事件，存在以下问题：

1. **时序问题**：`ExecutionListener` 的 `end` 事件在流程状态最终确定**之前**触发
2. **状态不准确**：此时获取的状态还是 `RUNNING(1)`，而不是最终的 `APPROVE(2)` 或 `REJECT(3)`
3. **逻辑复杂**：需要复杂的状态判断逻辑来推测是否审批通过

### 解决方案

新的 `BpmGenericFormChangeStatusListener` 使用 `BpmProcessInstanceStatusEventListener`：

1. **时机正确**：在流程状态最终确定**后**触发
2. **状态准确**：能获取到正确的最终状态
3. **逻辑简洁**：无需复杂的状态判断，直接检查 `event.getStatus()` 是否为 `APPROVE`

## 使用方式

### 1. 创建监听器类

```java
@Component
public class YourChangeStatusListener extends BpmGenericFormChangeStatusListener {
    
    @Override
    protected String getProcessDefinitionKey() {
        return "your_change_process_key"; // 您的变更流程定义Key
    }
    
    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        config.setRelationField("your_relation_field"); // 关联字段名
        return config;
    }
}
```

### 2. 配置参数说明

#### 基础配置

- **relationField**: 关联字段名，用于查找对应的台账流程实例
  ```java
  config.setRelationField("contract_id"); // 通过 contract_id 关联合同台账
  ```

#### 高级配置

- **changeRecordField**: 变更记录字段名，用于记录变更历史
  ```java
  config.setChangeRecordField("contractChangeRecord");
  ```

- **fieldMapping**: 字段映射关系，当变更表单字段名与台账字段名不同时使用
  ```java
  Map<String, String> fieldMapping = new HashMap<>();
  fieldMapping.put("new_contract_amount", "contract_amount");
  fieldMapping.put("new_contract_name", "contract_name");
  config.setFieldMapping(fieldMapping);
  ```

- **skipFields**: 跳过的字段列表，不需要同步到台账的字段
  ```java
  List<String> skipFields = Arrays.asList("approval_comment", "temp_field");
  config.setSkipFields(skipFields);
  ```

- **recordOnly**: 仅记录模式，只添加变更记录，不更新其他字段
  ```java
  config.setRecordOnly(true); // 仅记录模式
  ```

### 3. 完整示例

```java
@Component
public class ContractChangeStatusListener extends BpmGenericFormChangeStatusListener {
    
    @Override
    protected String getProcessDefinitionKey() {
        return "contract_change_process";
    }
    
    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // 基础配置
        config.setRelationField("contract_id");
        config.setChangeRecordField("contractChangeRecord");
        
        // 字段映射
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("new_amount", "contract_amount");
        fieldMapping.put("new_name", "contract_name");
        config.setFieldMapping(fieldMapping);
        
        // 跳过字段
        config.setSkipFields(Arrays.asList("approval_comment", "attachment"));
        
        return config;
    }
}
```

## 工作原理

### 1. 事件触发时机

```
流程结束 → 状态更新 → 发送事件 → 监听器处理
```

具体时序：
1. `ExecutionListener` 的 `end` 事件触发（状态可能还是 RUNNING）
2. `BpmProcessInstanceServiceImpl.processProcessInstanceCompleted()` 执行
3. 最终状态设置（RUNNING → APPROVE/REJECT/CANCEL）
4. `BpmProcessInstanceStatusEvent` 发送
5. `BpmProcessInstanceStatusEventListener` 接收事件（状态已确定）

### 2. 处理逻辑

1. **状态检查**：只处理 `APPROVE` 状态的事件
2. **配置获取**：调用子类的 `getListenerConfig()` 方法
3. **表单数据获取**：从流程实例中获取表单数据
4. **台账查找**：根据关联字段值查找台账流程实例
5. **数据更新**：更新台账流程实例的历史变量

### 3. 数据更新策略

- **特殊字段**：自动更新 `applicant`、`change_time`、`update_time`
- **表单字段**：根据配置更新或映射字段
- **变更记录**：如果配置了 `changeRecordField`，会添加变更记录
- **跳过字段**：系统字段和配置的跳过字段不会被更新

## 迁移指南

### 从 BpmGenericFormChangeListener 迁移

1. **移除 BPMN 配置**：删除 BPMN 文件中的 ExecutionListener 配置
2. **创建新监听器**：继承 `BpmGenericFormChangeStatusListener`
3. **实现抽象方法**：实现 `getProcessDefinitionKey()` 和 `getListenerConfig()`
4. **测试验证**：确保在正确的时机触发

### 配置对比

| 原配置方式 | 新配置方式 |
|-----------|-----------|
| BPMN 扩展字段 JSON | Java 代码配置 |
| `${bpmGenericFormChangeListener}` | 自动监听所有流程 |
| 复杂的状态判断逻辑 | 简单的状态检查 |

## 注意事项

1. **无需 BPMN 配置**：新监听器会自动监听所有流程状态变更事件
2. **流程定义Key过滤**：通过 `getProcessDefinitionKey()` 方法过滤需要处理的流程
3. **状态准确性**：确保在流程状态最终确定后才处理
4. **错误处理**：单个字段更新失败不会影响其他字段的处理
5. **性能考虑**：只有审批通过的流程才会执行完整的处理逻辑

## 常见问题

### Q: 为什么不直接修复原监听器？
A: 原监听器使用的 ExecutionListener 架构本身就有时序问题，需要从根本上改变监听方式。

### Q: 新监听器是否兼容原配置？
A: 功能完全兼容，但配置方式从 BPMN JSON 改为 Java 代码配置，更加类型安全。

### Q: 如何确保监听器只处理特定流程？
A: 通过重写 `getProcessDefinitionKey()` 方法返回需要处理的流程定义Key。

### Q: 是否支持多个流程定义？
A: 当前版本支持单个流程定义，如需支持多个，可以创建多个监听器类。
