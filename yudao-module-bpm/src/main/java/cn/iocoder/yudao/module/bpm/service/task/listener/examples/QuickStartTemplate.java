package cn.iocoder.yudao.module.bpm.service.task.listener.examples;

import cn.iocoder.yudao.module.bpm.service.task.listener.BpmGenericFormChangeStatusListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 快速配置模板 - 复制此模板快速创建您的监听器
 * 
 * 使用步骤：
 * 1. 复制此类并重命名
 * 2. 修改 getProcessDefinitionKey() 返回您的流程定义Key
 * 3. 根据需要配置 getListenerConfig() 方法
 * 4. 添加 @Component 注解
 * 
 * <AUTHOR>
 */
// @Component  // 取消注释并重命名类后启用
public class QuickStartTemplate extends BpmGenericFormChangeStatusListener {

    @Override
    protected String getProcessDefinitionKey() {
        // TODO: 修改为您的流程定义Key
        return "your_process_definition_key";
    }

    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // ========== 必填配置 ==========
        
        // TODO: 修改为您的关联字段名
        config.setRelationField("your_relation_field");
        
        
        // ========== 可选配置 ==========
        
        // 1. 变更记录字段（如果需要记录变更历史）
        // config.setChangeRecordField("yourChangeRecordField");
        
        // 2. 字段映射（如果变更表单字段名与台账字段名不同）
        // Map<String, String> fieldMapping = new HashMap<>();
        // fieldMapping.put("变更表单字段名", "台账字段名");
        // fieldMapping.put("new_name", "name");
        // fieldMapping.put("new_amount", "amount");
        // config.setFieldMapping(fieldMapping);
        
        // 3. 跳过字段（不需要同步的字段）
        // config.setSkipFields(Arrays.asList("approval_comment", "temp_field"));
        
        // 4. 处理模式（默认false：完整更新模式）
        // config.setRecordOnly(false);  // false=更新字段+记录历史, true=仅记录历史
        
        return config;
    }
}

/**
 * 实际使用示例 - 合同变更监听器
 */
@Component
class ContractChangeExampleListener extends BpmGenericFormChangeStatusListener {

    @Override
    protected String getProcessDefinitionKey() {
        return "contract_change_process";
    }

    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // 必填：关联字段
        config.setRelationField("contract_id");
        
        // 可选：记录变更历史
        config.setChangeRecordField("contractChangeHistory");
        
        // 可选：字段映射
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("new_contract_name", "contract_name");
        fieldMapping.put("new_contract_amount", "contract_amount");
        config.setFieldMapping(fieldMapping);
        
        // 可选：跳过字段
        config.setSkipFields(Arrays.asList("approval_comment", "attachment_ids"));
        
        return config;
    }
}

/**
 * 最简配置示例 - 直接同步同名字段
 */
@Component
class SimpleExampleListener extends BpmGenericFormChangeStatusListener {

    @Override
    protected String getProcessDefinitionKey() {
        return "simple_change_process";
    }

    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        config.setRelationField("target_id");  // 只需要配置关联字段
        return config;
    }
}
