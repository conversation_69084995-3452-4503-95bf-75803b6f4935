package cn.iocoder.yudao.module.bpm.service.task.listener.examples;

import cn.iocoder.yudao.module.bpm.service.task.listener.BpmGenericFormChangeStatusListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 仅记录模式示例 - 只记录变更历史，不更新台账字段
 * 
 * 适用场景：
 * - 只需要记录变更历史
 * - 不需要更新台账的实际数据
 * - 审计和追踪需求
 * 
 * <AUTHOR>
 */
@Component
public class RecordOnlyExampleListener extends BpmGenericFormChangeStatusListener {

    @Override
    protected String getProcessDefinitionKey() {
        // 处理员工信息变更流程
        return "employee_info_change_process";
    }

    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // 1. 基础配置：关联字段
        config.setRelationField("employee_id");
        
        // 2. 变更记录配置：记录变更历史
        config.setChangeRecordField("employeeChangeHistory");
        
        // 3. 字段映射配置：只映射需要记录的关键字段
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("old_department", "original_department");        // 原部门
        fieldMapping.put("new_department", "target_department");          // 新部门
        fieldMapping.put("old_position", "original_position");           // 原职位
        fieldMapping.put("new_position", "target_position");             // 新职位
        fieldMapping.put("change_reason", "reason");                     // 变更原因
        fieldMapping.put("effective_date", "effective_date");            // 生效日期
        config.setFieldMapping(fieldMapping);
        
        // 4. 仅记录模式：只添加变更记录，不更新台账字段
        config.setRecordOnly(true);
        
        return config;
    }
}
