package cn.iocoder.yudao.module.bpm.service.task.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.bpm.api.event.BpmProcessInstanceStatusEvent;
import cn.iocoder.yudao.module.bpm.api.event.BpmProcessInstanceStatusEventListener;
import cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.jdbc.core.JdbcTemplate;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用表单变更审批状态监听器（完整版本）
 * 审批通过后，关联到台账流程实例并同步基础数据（使用Flowable历史变量作为台账数据）
 *
 * 这是原 BpmGenericFormChangeListener 的正确版本，使用 BpmProcessInstanceStatusEventListener
 * 来确保在流程状态最终确定后才触发处理逻辑。
 *
 * 使用方式：
 * 1. 继承此类并实现 getProcessDefinitionKey() 方法
 * 2. 在子类中配置要监听的流程定义Key
 * 3. 在子类中配置监听器配置（通过重写 getListenerConfig() 方法）
 *
 * 示例：
 * ```java
 * @Component
 * public class ContractChangeStatusListener extends BpmGenericFormChangeStatusListener {
 *     @Override
 *     protected String getProcessDefinitionKey() {
 *         return "contract_change_process";
 *     }
 *
 *     @Override
 *     protected ListenerConfig getListenerConfig() {
 *         ListenerConfig config = new ListenerConfig();
 *         config.setRelationField("contract_id");
 *         return config;
 *     }
 * }
 * ```
 *
 * 工作原理：
 * 1. 监听流程实例状态变更事件（在状态最终确定后触发）
 * 2. 检查流程是否审批通过
 * 3. 获取变更流程的表单数据
 * 4. 根据关联字段值查找对应的台账流程实例
 * 5. 更新台账流程实例的历史变量：
 *    - applicant: 更新为当前流程发起人
 *    - change_time: 更新为当前时间
 *    - 其他字段: 直接覆盖（除了特殊字段）
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BpmGenericFormChangeStatusListener extends BpmProcessInstanceStatusEventListener {

    @Resource
    private HistoryService historyService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private JdbcTemplate jdbcTemplate;

    /**
     * 监听器配置参数（与原 BpmGenericFormChangeListener 保持一致）
     */
    @Setter
    @Getter
    public static class ListenerConfig {
        private String relationField;
        private String changeRecordField; // 变更记录字段名，如 "basicInfoChangeRecord"
        private Map<String, String> fieldMapping; // 字段映射关系，如 {"a": "b"}
        private java.util.List<String> skipFields; // 跳过的字段列表
        private Boolean recordOnly; // 仅记录模式，只添加变更记录，不更新其他字段

        @Override
        public String toString() {
            return "ListenerConfig{" +
                    "relationField='" + relationField + '\'' +
                    ", changeRecordField='" + changeRecordField + '\'' +
                    ", fieldMapping=" + fieldMapping +
                    ", skipFields=" + skipFields +
                    ", recordOnly=" + recordOnly +
                    '}';
        }
    }

    /**
     * 获取监听器配置
     * 子类需要实现此方法，返回监听器配置
     */
    protected abstract ListenerConfig getListenerConfig();

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        // 只处理审批通过的情况
        if (!BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(event.getStatus())) {
            log.debug("[onEvent][流程未通过审批，跳过处理] processInstanceId={}, status={}",
                    event.getId(), event.getStatus());
            return;
        }

        log.info("=== BpmGenericFormChangeStatusListener.onEvent() 被调用 ===");
        log.info("流程实例ID: {}", event.getId());
        log.info("流程状态: {}", event.getStatus());

        try {
            // 1. 获取监听器配置
            ListenerConfig config = getListenerConfig();
            if (config == null) {
                log.warn("监听器配置为空，跳过处理");
                return;
            }
            log.info("监听器配置: {}", config);

            // 2. 获取流程实例
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(event.getId())
                    .includeProcessVariables()
                    .singleResult();

            if (processInstance == null) {
                log.warn("流程实例不存在: {}", event.getId());
                return;
            }

            log.info("确认流程审核通过，继续处理...");

            // 3. 获取表单数据
            log.info("获取表单数据...");
            Map<String, Object> formVariables = FlowableUtils.getProcessInstanceFormVariable(processInstance);
            log.info("表单数据: {}", formVariables);
            if (CollUtil.isEmpty(formVariables)) {
                log.warn("流程实例 {} 没有表单数据", event.getId());
                return;
            }

            // 4. 更新台账流程实例
            log.info("开始更新台账流程实例...");
            updateLedgerProcessInstance(config, formVariables, processInstance);

            log.info("表单变更审批监听器处理完成，流程实例: {}, 关联字段: {}",
                    event.getId(), config.getRelationField());

        } catch (Exception e) {
            log.error("表单变更审批监听器处理失败，流程实例: {}", event.getId(), e);
        }
    }

    /**
     * 更新台账流程实例（与原 BpmGenericFormChangeListener 逻辑完全一致）
     */
    private void updateLedgerProcessInstance(ListenerConfig config, Map<String, Object> formVariables, HistoricProcessInstance changeProcessInstance) {
        try {
            log.info("开始更新台账流程实例，配置: {}", config);

            // 获取关联字段值
            Object relationValue = formVariables.get(config.getRelationField());
            log.info("关联字段 {} 的值: {}", config.getRelationField(), relationValue);

            if (relationValue == null) {
                log.warn("关联字段 {} 的值为空，无法更新台账数据", config.getRelationField());
                return;
            }

            // 直接通过流程实例ID查找台账流程实例
            log.info("通过流程实例ID查找台账流程实例，ID: {}", relationValue);

            HistoricProcessInstance ledgerProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(relationValue.toString())
                    .includeProcessVariables()
                    .singleResult();

            if (ledgerProcessInstance == null) {
                log.error("未找到台账流程实例！");
                log.error("关联字段: {}, 流程实例ID: {}", config.getRelationField(), relationValue);
                log.error("请检查流程实例ID是否正确，以及台账流程实例是否存在");
                return;
            }

            log.info("成功找到台账流程实例: {}, 名称: {}, 状态: {}",
                    ledgerProcessInstance.getId(),
                    ledgerProcessInstance.getName(),
                    ledgerProcessInstance.getEndTime() != null ? "已完成" : "进行中");

            // 更新台账流程实例的历史变量
            updateLedgerVariables(ledgerProcessInstance.getId(), formVariables, changeProcessInstance, config);

            log.info("成功更新台账流程实例，台账流程ID: {}, 台账流程名称: {}, 关联字段: {}, 关联值: {}",
                    ledgerProcessInstance.getId(), ledgerProcessInstance.getName(), config.getRelationField(), relationValue);

        } catch (Exception e) {
            log.error("更新台账流程实例失败，关联字段: {}", config.getRelationField(), e);
            throw new RuntimeException("更新台账流程实例失败", e);
        }
    }

    /**
     * 更新台账流程实例的历史变量（与原 BpmGenericFormChangeListener 逻辑完全一致）
     */
    private void updateLedgerVariables(String ledgerProcessInstanceId, Map<String, Object> formVariables,
                                     HistoricProcessInstance changeProcessInstance, ListenerConfig config) {
        try {
            log.info("开始更新台账历史变量，台账流程: {}", ledgerProcessInstanceId);

            // 检查是否为仅记录模式
            boolean isRecordOnly = config.getRecordOnly() != null && config.getRecordOnly();
            log.info("处理模式: {}", isRecordOnly ? "仅记录模式" : "完整更新模式");

            if (!isRecordOnly) {
                // 1. 更新表单字段（根据配置跳过指定字段）
                log.info("更新表单字段...");
                for (Map.Entry<String, Object> entry : formVariables.entrySet()) {
                    String fieldName = entry.getKey();

                    // 检查是否在跳过字段列表中
                    if (shouldSkipField(fieldName, config)) {
                        log.debug("跳过配置的字段: {}", fieldName);
                        continue;
                    }

                    // 检查台账中是否已存在该变量
                    if (!isVariableExistsInLedger(ledgerProcessInstanceId, fieldName)) {
                        log.debug("台账中不存在字段 {}，跳过更新", fieldName);
                        continue;
                    }

                    log.info("更新字段: {} = {}", fieldName, entry.getValue());
                    updateHistoricVariable(ledgerProcessInstanceId, fieldName, entry.getValue());
                }

                // 2. 更新特殊字段：申请人、变更时间
                log.info("更新特殊字段...");
                updateHistoricVariable(ledgerProcessInstanceId, "applicant", changeProcessInstance.getStartUserId());
                updateHistoricVariable(ledgerProcessInstanceId, "change_time", LocalDateTime.now());
                updateHistoricVariable(ledgerProcessInstanceId, "update_time", LocalDateTime.now());
            } else {
                log.info("仅记录模式：跳过字段更新，只处理变更记录");
            }

            // 3. 处理变更记录字段（如果配置了的话）
            if (StrUtil.isNotBlank(config.getChangeRecordField())) {
                log.info("处理变更记录字段: {}", config.getChangeRecordField());
                addChangeRecord(ledgerProcessInstanceId, formVariables, changeProcessInstance, config);
            }

        } catch (Exception e) {
            log.error("更新台账历史变量失败，台账流程: {}", ledgerProcessInstanceId, e);
            throw e;
        }
    }

    /**
     * 检查是否应该跳过字段（与原 BpmGenericFormChangeListener 逻辑完全一致）
     */
    private boolean shouldSkipField(String fieldName, ListenerConfig config) {
        // 检查是否在跳过字段列表中
        if (config.getSkipFields() != null && config.getSkipFields().contains(fieldName)) {
            return true;
        }

        // 检查是否为系统字段
        return "applicant".equals(fieldName) ||
               "change_time".equals(fieldName) ||
               "update_time".equals(fieldName) ||
               "original_process_instance_id".equals(fieldName) ||
               "original_process_key".equals(fieldName) ||
               "original_process_name".equals(fieldName) ||
               "business_type".equals(fieldName) ||
               "status".equals(fieldName) ||
               "PROCESS_STATUS".equals(fieldName);
    }

    /**
     * 检查台账中是否存在指定变量（与原 BpmGenericFormChangeListener 逻辑完全一致）
     */
    private boolean isVariableExistsInLedger(String processInstanceId, String variableName) {
        try {
            List<Map<String, Object>> result = jdbcTemplate.queryForList(
                    "SELECT COUNT(*) as count FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = ? AND NAME_ = ?",
                    processInstanceId, variableName);

            if (CollUtil.isNotEmpty(result)) {
                Number count = (Number) result.get(0).get("count");
                return count != null && count.intValue() > 0;
            }
            return false;
        } catch (Exception e) {
            log.error("检查变量是否存在失败: processInstanceId={}, variableName={}", processInstanceId, variableName, e);
            return false;
        }
    }

    /**
     * 更新历史变量（与原 BpmGenericFormChangeListener 逻辑完全一致）
     */
    private void updateHistoricVariable(String processInstanceId, String variableName, Object value) {
        try {
            // 处理值的序列化，避免双重JSON序列化
            String jsonValue;
            if (value instanceof String) {
                // 如果已经是字符串，直接使用
                jsonValue = (String) value;
            } else if (value instanceof Number || value instanceof Boolean) {
                // 数字和布尔值转为字符串
                jsonValue = String.valueOf(value);
            } else {
                // 复杂对象才进行JSON序列化
                jsonValue = JsonUtils.toJsonString(value);
            }

            // 检查是否已存在该变量
            List<Map<String, Object>> existingVars = jdbcTemplate.queryForList(
                    "SELECT ID_ FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = ? AND NAME_ = ?",
                    processInstanceId, variableName);

            if (CollUtil.isNotEmpty(existingVars)) {
                // 更新现有的历史变量
                String sql = "UPDATE ACT_HI_VARINST SET TEXT_ = ?, LAST_UPDATED_TIME_ = NOW() WHERE PROC_INST_ID_ = ? AND NAME_ = ?";
                int updated = jdbcTemplate.update(sql, jsonValue, processInstanceId, variableName);
                log.debug("更新历史变量: processInstanceId={}, variableName={}, updated={}",
                        processInstanceId, variableName, updated);
            } else {
                // 插入新的历史变量记录
                String sql = "INSERT INTO ACT_HI_VARINST (ID_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, NAME_, VAR_TYPE_, REV_, BYTEARRAY_ID_, DOUBLE_, LONG_, TEXT_, TEXT2_, CREATE_TIME_, LAST_UPDATED_TIME_) " +
                           "VALUES (?, ?, ?, NULL, ?, 'string', 1, NULL, NULL, NULL, ?, NULL, NOW(), NOW())";

                // 生成标准的UUID格式ID（与Flowable引擎保持一致）
                String varId = java.util.UUID.randomUUID().toString();

                log.debug("生成历史变量ID: {}, 长度: {}", varId, varId.length());

                int inserted = jdbcTemplate.update(sql, varId, processInstanceId, processInstanceId, variableName, jsonValue);
                log.debug("插入历史变量: processInstanceId={}, variableName={}, varId={}, inserted={}",
                        processInstanceId, variableName, varId, inserted);
            }
        } catch (Exception e) {
            log.error("更新历史变量失败: processInstanceId={}, variableName={}, value={}",
                    processInstanceId, variableName, value, e);
            // 不抛出异常，避免影响其他字段的更新
            log.warn("跳过字段 {} 的更新，继续处理其他字段", variableName);
        }
    }

    /**
     * 添加变更记录到台账流程实例（与原 BpmGenericFormChangeListener 逻辑完全一致）
     */
    private void addChangeRecord(String ledgerProcessInstanceId, Map<String, Object> formVariables,
                               HistoricProcessInstance changeProcessInstance, ListenerConfig config) {
        try {
            log.info("开始添加变更记录，变更记录字段: {}", config.getChangeRecordField());

            // 1. 获取台账流程实例中现有的变更记录
            List<Map> existingChangeRecords = getExistingChangeRecords(ledgerProcessInstanceId, config.getChangeRecordField());
            log.info("台账中现有的变更记录数量: {}", existingChangeRecords.size());

            // 2. 根据字段映射构建新的变更记录
            Map<String, Object> newChangeRecord = buildChangeRecord(formVariables, changeProcessInstance, config);
            log.info("构建的新变更记录: {}", newChangeRecord);

            if (newChangeRecord.isEmpty()) {
                log.warn("没有构建出有效的变更记录，跳过添加");
                return;
            }

            // 3. 将新记录添加到现有记录列表中
            existingChangeRecords.add(newChangeRecord);

            // 4. 更新台账流程实例的变更记录字段
            updateHistoricVariable(ledgerProcessInstanceId, config.getChangeRecordField(), existingChangeRecords);

            log.info("成功添加变更记录，台账流程: {}, 变更记录字段: {}, 新记录数量: {}",
                    ledgerProcessInstanceId, config.getChangeRecordField(), existingChangeRecords.size());

        } catch (Exception e) {
            log.error("添加变更记录失败，台账流程: {}, 变更记录字段: {}",
                    ledgerProcessInstanceId, config.getChangeRecordField(), e);
            // 不抛出异常，避免影响其他处理
            log.warn("跳过变更记录的添加，继续处理其他逻辑");
        }
    }

    /**
     * 获取现有的变更记录（与原 BpmGenericFormChangeListener 逻辑完全一致）
     */
    private List<Map> getExistingChangeRecords(String processInstanceId, String changeRecordField) {
        try {
            List<Map<String, Object>> result = jdbcTemplate.queryForList(
                    "SELECT TEXT_ FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = ? AND NAME_ = ?",
                    processInstanceId, changeRecordField);

            if (CollUtil.isNotEmpty(result)) {
                String jsonText = (String) result.get(0).get("TEXT_");
                if (StrUtil.isNotBlank(jsonText)) {
                    // 尝试解析为List
                    try {
                        return JsonUtils.parseArray(jsonText, Map.class);
                    } catch (Exception e) {
                        log.warn("解析现有变更记录失败，将创建新的记录列表: {}", e.getMessage());
                    }
                }
            }

            // 如果没有现有记录或解析失败，返回空列表
            return new java.util.ArrayList<>();

        } catch (Exception e) {
            log.error("获取现有变更记录失败: processInstanceId={}, changeRecordField={}",
                    processInstanceId, changeRecordField, e);
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 构建变更记录（与原 BpmGenericFormChangeListener 逻辑完全一致）
     */
    private Map<String, Object> buildChangeRecord(Map<String, Object> formVariables,
                                                 HistoricProcessInstance changeProcessInstance,
                                                 ListenerConfig config) {
        Map<String, Object> changeRecord = new HashMap<>();

        try {
            // 添加基础信息
            changeRecord.put("change_time", LocalDateTime.now());
            changeRecord.put("change_user", changeProcessInstance.getStartUserId());
            changeRecord.put("process_instance_id", changeProcessInstance.getId());

            // 根据字段映射添加变更数据
            if (config.getFieldMapping() != null && !config.getFieldMapping().isEmpty()) {
                for (Map.Entry<String, String> mapping : config.getFieldMapping().entrySet()) {
                    String sourceField = mapping.getKey();
                    String targetField = mapping.getValue();

                    Object value = formVariables.get(sourceField);
                    if (value != null) {
                        changeRecord.put(targetField, value);
                        log.debug("映射字段: {} -> {} = {}", sourceField, targetField, value);
                    }
                }
            } else {
                // 如果没有配置字段映射，直接复制所有表单字段（除了系统字段）
                for (Map.Entry<String, Object> entry : formVariables.entrySet()) {
                    String fieldName = entry.getKey();
                    if (!shouldSkipField(fieldName, config)) {
                        changeRecord.put(fieldName, entry.getValue());
                    }
                }
            }

            log.info("构建变更记录完成，字段数量: {}", changeRecord.size());
            return changeRecord;

        } catch (Exception e) {
            log.error("构建变更记录失败", e);
            return new HashMap<>();
        }
    }
}
