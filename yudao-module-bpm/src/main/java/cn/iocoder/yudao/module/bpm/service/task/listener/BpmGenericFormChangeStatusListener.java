package cn.iocoder.yudao.module.bpm.service.task.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.bpm.api.event.BpmProcessInstanceStatusEvent;
import cn.iocoder.yudao.module.bpm.api.event.BpmProcessInstanceStatusEventListener;
import cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用表单变更审批状态监听器
 * 审批通过后，关联到台账流程实例并同步基础数据（使用Flowable历史变量作为台账数据）
 * 
 * 使用方式：
 * 1. 继承此类并实现 getProcessDefinitionKey() 方法
 * 2. 在子类中配置要监听的流程定义Key
 * 3. 在子类中配置关联字段名（relationField）
 * 
 * 示例：
 * ```java
 * @Component
 * public class ContractChangeStatusListener extends BpmGenericFormChangeStatusListener {
 *     @Override
 *     protected String getProcessDefinitionKey() {
 *         return "contract_change_process";
 *     }
 *     
 *     @Override
 *     protected String getRelationField() {
 *         return "contract_id";
 *     }
 * }
 * ```
 * 
 * 工作原理：
 * 1. 监听流程实例状态变更事件（在状态最终确定后触发）
 * 2. 检查流程是否审批通过
 * 3. 获取变更流程的表单数据
 * 4. 根据关联字段值查找对应的台账流程实例
 * 5. 更新台账流程实例的历史变量：
 *    - applicant: 更新为当前流程发起人
 *    - change_time: 更新为当前时间
 *    - 其他字段: 直接覆盖（除了特殊字段）
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BpmGenericFormChangeStatusListener extends BpmProcessInstanceStatusEventListener {

    @Resource
    private HistoryService historyService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 监听器配置
     */
    @Data
    public static class ListenerConfig {
        /**
         * 关联字段名，用于查找对应的台账流程实例
         */
        private String relationField;
    }

    /**
     * 获取关联字段名
     * 子类需要实现此方法，返回用于关联台账流程实例的字段名
     */
    protected abstract String getRelationField();

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        // 只处理审批通过的情况
        if (!BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(event.getStatus())) {
            log.debug("[onEvent][流程未通过审批，跳过处理] processInstanceId={}, status={}", 
                    event.getId(), event.getStatus());
            return;
        }

        log.info("[onEvent][流程审批通过，开始处理表单变更] processInstanceId={}", event.getId());

        try {
            // 1. 获取流程实例
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(event.getId())
                    .singleResult();

            if (processInstance == null) {
                log.warn("流程实例不存在: {}", event.getId());
                return;
            }

            // 2. 获取表单数据
            Map<String, Object> formVariables = FlowableUtils.getProcessInstanceFormVariable(processInstance);
            log.info("表单数据: {}", formVariables);
            if (CollUtil.isEmpty(formVariables)) {
                log.warn("流程实例 {} 没有表单数据", event.getId());
                return;
            }

            // 3. 获取关联字段值
            String relationField = getRelationField();
            if (StrUtil.isBlank(relationField)) {
                log.warn("关联字段名为空，跳过处理");
                return;
            }

            Object relationValue = formVariables.get(relationField);
            if (relationValue == null) {
                log.warn("关联字段 {} 的值为空，跳过处理", relationField);
                return;
            }

            log.info("关联字段: {}, 关联值: {}", relationField, relationValue);

            // 4. 查找对应的台账流程实例
            List<HistoricProcessInstance> ledgerInstances = historyService.createHistoricProcessInstanceQuery()
                    .variableValueEquals(relationField, relationValue)
                    .processDefinitionKeyLike("%_ledger%")
                    .list();

            if (CollUtil.isEmpty(ledgerInstances)) {
                log.warn("未找到关联的台账流程实例，关联字段: {}, 关联值: {}", relationField, relationValue);
                return;
            }

            // 5. 更新台账流程实例的历史变量
            for (HistoricProcessInstance ledgerInstance : ledgerInstances) {
                updateLedgerProcessInstance(ledgerInstance, formVariables, processInstance);
            }

            log.info("表单变更处理完成，流程实例: {}", event.getId());

        } catch (Exception e) {
            log.error("表单变更处理失败，流程实例: {}", event.getId(), e);
        }
    }

    /**
     * 更新台账流程实例的历史变量
     */
    private void updateLedgerProcessInstance(HistoricProcessInstance ledgerInstance, 
                                           Map<String, Object> changeFormVariables, 
                                           HistoricProcessInstance changeProcessInstance) {
        try {
            log.info("开始更新台账流程实例: {}", ledgerInstance.getId());

            // 获取台账流程实例的当前变量
            Map<String, Object> ledgerVariables = new HashMap<>(ledgerInstance.getProcessVariables());

            // 更新特殊字段
            ledgerVariables.put("applicant", changeProcessInstance.getStartUserId());
            ledgerVariables.put("change_time", LocalDateTime.now());

            // 更新其他字段（除了特殊字段）
            for (Map.Entry<String, Object> entry : changeFormVariables.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 跳过特殊字段
                if (isSpecialField(key)) {
                    continue;
                }

                ledgerVariables.put(key, value);
                log.debug("更新字段: {} = {}", key, value);
            }

            // 使用 RuntimeService 更新变量（如果流程实例还在运行）
            // 或者使用 HistoryService 更新历史变量
            if (ledgerInstance.getEndTime() == null) {
                // 流程实例还在运行，使用 RuntimeService
                runtimeService.setVariables(ledgerInstance.getId(), ledgerVariables);
                log.info("已更新运行中的台账流程实例变量: {}", ledgerInstance.getId());
            } else {
                // 流程实例已结束，需要通过其他方式更新历史变量
                // 注意：Flowable 不直接支持更新历史变量，这里可能需要自定义实现
                log.warn("台账流程实例已结束，无法直接更新历史变量: {}", ledgerInstance.getId());
            }

        } catch (Exception e) {
            log.error("更新台账流程实例失败: {}", ledgerInstance.getId(), e);
        }
    }

    /**
     * 判断是否为特殊字段（不需要从变更表单覆盖的字段）
     */
    private boolean isSpecialField(String fieldName) {
        return "applicant".equals(fieldName) || 
               "change_time".equals(fieldName) ||
               "original_process_instance_id".equals(fieldName) ||
               "original_process_key".equals(fieldName) ||
               "original_process_name".equals(fieldName) ||
               "business_type".equals(fieldName) ||
               "status".equals(fieldName) ||
               "PROCESS_STATUS".equals(fieldName);
    }
}
