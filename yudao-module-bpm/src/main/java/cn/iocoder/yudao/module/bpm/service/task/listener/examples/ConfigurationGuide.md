# BpmGenericFormChangeStatusListener 配置指南

## 配置参数详解

### 1. relationField（必填）
**用途**：指定用于关联台账流程实例的字段名

```java
config.setRelationField("contract_id");
```

**说明**：
- 变更流程表单中必须包含此字段
- 字段值应该是台账流程实例的ID
- 系统会根据此字段值查找对应的台账流程实例

### 2. changeRecordField（可选）
**用途**：指定用于存储变更记录的字段名

```java
config.setChangeRecordField("contractChangeHistory");
```

**说明**：
- 如果配置了此字段，系统会在台账中记录变更历史
- 变更记录以JSON数组形式存储
- 每条记录包含变更时间、变更人、变更内容等信息

### 3. fieldMapping（可选）
**用途**：定义变更表单字段到台账字段的映射关系

```java
Map<String, String> fieldMapping = new HashMap<>();
fieldMapping.put("new_contract_name", "contract_name");    // 变更表单字段 -> 台账字段
fieldMapping.put("new_amount", "contract_amount");
config.setFieldMapping(fieldMapping);
```

**说明**：
- Key：变更流程表单中的字段名
- Value：台账流程实例中的字段名
- 如果不配置，系统会直接同步同名字段

### 4. skipFields（可选）
**用途**：指定不需要同步到台账的字段列表

```java
config.setSkipFields(Arrays.asList(
    "approval_comment",    // 审批意见
    "attachment_ids",      // 附件ID
    "temp_field"          // 临时字段
));
```

**说明**：
- 列表中的字段不会被同步到台账
- 系统字段（如applicant、change_time等）会自动跳过

### 5. recordOnly（可选）
**用途**：设置是否为仅记录模式

```java
config.setRecordOnly(true);   // 仅记录模式
config.setRecordOnly(false);  // 完整更新模式（默认）
```

**说明**：
- `true`：只添加变更记录，不更新台账字段
- `false`：既更新台账字段，又添加变更记录（如果配置了changeRecordField）

## 配置组合策略

### 策略1：完整同步
```java
ListenerConfig config = new ListenerConfig();
config.setRelationField("target_id");
// 不配置其他参数，直接同步所有同名字段
```

### 策略2：字段映射同步
```java
ListenerConfig config = new ListenerConfig();
config.setRelationField("contract_id");
Map<String, String> mapping = new HashMap<>();
mapping.put("new_name", "contract_name");
config.setFieldMapping(mapping);
```

### 策略3：选择性同步
```java
ListenerConfig config = new ListenerConfig();
config.setRelationField("supplier_id");
config.setSkipFields(Arrays.asList("internal_note", "temp_data"));
```

### 策略4：历史记录
```java
ListenerConfig config = new ListenerConfig();
config.setRelationField("customer_id");
config.setChangeRecordField("changeHistory");
config.setRecordOnly(true);  // 只记录，不更新
```

### 策略5：混合模式
```java
ListenerConfig config = new ListenerConfig();
config.setRelationField("project_id");
config.setChangeRecordField("projectChanges");
Map<String, String> mapping = new HashMap<>();
mapping.put("new_budget", "budget");
config.setFieldMapping(mapping);
config.setSkipFields(Arrays.asList("confidential"));
// recordOnly默认为false，既更新又记录
```

## 实际使用示例

### 示例1：合同变更流程
```java
@Component
public class ContractChangeListener extends BpmGenericFormChangeStatusListener {
    
    @Override
    protected String getProcessDefinitionKey() {
        return "contract_change_process";
    }
    
    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // 通过合同ID关联
        config.setRelationField("contract_id");
        
        // 记录变更历史
        config.setChangeRecordField("contractChangeHistory");
        
        // 字段映射：变更表单 -> 合同台账
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("new_contract_name", "contract_name");
        fieldMapping.put("new_contract_amount", "contract_amount");
        fieldMapping.put("new_start_date", "start_date");
        fieldMapping.put("new_end_date", "end_date");
        config.setFieldMapping(fieldMapping);
        
        // 跳过不需要的字段
        config.setSkipFields(Arrays.asList("approval_comment", "attachment_list"));
        
        return config;
    }
}
```

### 示例2：员工信息变更（仅记录）
```java
@Component
public class EmployeeChangeListener extends BpmGenericFormChangeStatusListener {
    
    @Override
    protected String getProcessDefinitionKey() {
        return "employee_change_process";
    }
    
    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        config.setRelationField("employee_id");
        config.setChangeRecordField("employeeChangeHistory");
        config.setRecordOnly(true);  // 只记录变更，不更新员工档案
        
        // 只记录关键变更信息
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("old_department", "from_department");
        fieldMapping.put("new_department", "to_department");
        fieldMapping.put("change_reason", "reason");
        config.setFieldMapping(fieldMapping);
        
        return config;
    }
}
```

## 注意事项

1. **relationField 必须存在**：变更流程表单中必须包含关联字段
2. **台账流程实例必须存在**：系统会根据关联字段值查找台账流程实例
3. **字段类型兼容**：确保映射的字段类型兼容
4. **权限考虑**：确保监听器有权限访问数据库
5. **性能考虑**：大量数据变更时注意性能影响

## 常见问题

**Q: 如果台账流程实例不存在怎么办？**
A: 系统会记录错误日志并跳过处理，不会影响变更流程的正常完成。

**Q: 字段映射时如果目标字段不存在怎么办？**
A: 系统会跳过该字段的更新，继续处理其他字段。

**Q: 可以同时配置多个监听器吗？**
A: 可以，每个监听器处理不同的流程定义Key。

**Q: 如何调试配置是否正确？**
A: 查看日志输出，系统会详细记录处理过程和结果。
