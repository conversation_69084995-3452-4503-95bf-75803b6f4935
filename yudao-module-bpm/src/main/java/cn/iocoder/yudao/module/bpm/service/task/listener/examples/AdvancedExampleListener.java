package cn.iocoder.yudao.module.bpm.service.task.listener.examples;

import cn.iocoder.yudao.module.bpm.service.task.listener.BpmGenericFormChangeStatusListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 完整配置示例 - 展示所有配置选项
 * 
 * 适用场景：
 * - 复杂的表单变更同步
 * - 需要字段映射
 * - 需要记录变更历史
 * - 需要跳过某些字段
 * 
 * <AUTHOR>
 */
@Component
public class AdvancedExampleListener extends BpmGenericFormChangeStatusListener {

    @Override
    protected String getProcessDefinitionKey() {
        // 处理合同变更流程
        return "contract_change_process";
    }

    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // 1. 基础配置：关联字段
        config.setRelationField("contract_id");
        
        // 2. 变更记录配置：记录变更历史
        config.setChangeRecordField("contractChangeHistory");
        
        // 3. 字段映射配置：变更表单字段名 -> 台账字段名
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("new_contract_name", "contract_name");           // 新合同名称 -> 合同名称
        fieldMapping.put("new_contract_amount", "contract_amount");       // 新合同金额 -> 合同金额
        fieldMapping.put("new_contract_type", "contract_type");           // 新合同类型 -> 合同类型
        fieldMapping.put("new_start_date", "start_date");                 // 新开始日期 -> 开始日期
        fieldMapping.put("new_end_date", "end_date");                     // 新结束日期 -> 结束日期
        fieldMapping.put("new_party_a", "party_a");                       // 新甲方 -> 甲方
        fieldMapping.put("new_party_b", "party_b");                       // 新乙方 -> 乙方
        fieldMapping.put("new_description", "description");               // 新描述 -> 描述
        config.setFieldMapping(fieldMapping);
        
        // 4. 跳过字段配置：不需要同步到台账的字段
        config.setSkipFields(Arrays.asList(
            "approval_comment",        // 审批意见
            "attachment_ids",          // 附件ID列表
            "change_reason",           // 变更原因（只在变更记录中保存）
            "temp_field",              // 临时字段
            "workflow_data"            // 工作流数据
        ));
        
        // 5. 处理模式：完整更新模式（默认）
        config.setRecordOnly(false);
        
        return config;
    }
}
