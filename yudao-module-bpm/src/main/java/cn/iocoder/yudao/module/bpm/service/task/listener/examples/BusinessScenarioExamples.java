package cn.iocoder.yudao.module.bpm.service.task.listener.examples;

import cn.iocoder.yudao.module.bpm.service.task.listener.BpmGenericFormChangeStatusListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 多种业务场景配置示例
 * 
 * <AUTHOR>
 */
public class BusinessScenarioExamples {

    /**
     * 场景1：合同变更 - 完整字段映射
     */
    @Component
    public static class ContractChangeListener extends BpmGenericFormChangeStatusListener {
        
        @Override
        protected String getProcessDefinitionKey() {
            return "contract_change_process";
        }

        @Override
        protected ListenerConfig getListenerConfig() {
            ListenerConfig config = new ListenerConfig();
            config.setRelationField("contract_id");
            config.setChangeRecordField("contractChangeRecord");
            
            // 详细的字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("new_amount", "contract_amount");
            fieldMapping.put("new_name", "contract_name");
            fieldMapping.put("new_type", "contract_type");
            fieldMapping.put("new_start_date", "start_date");
            fieldMapping.put("new_end_date", "end_date");
            config.setFieldMapping(fieldMapping);
            
            config.setSkipFields(Arrays.asList("approval_comment", "attachment"));
            return config;
        }
    }

    /**
     * 场景2：供应商信息变更 - 简单同步
     */
    @Component
    public static class SupplierChangeListener extends BpmGenericFormChangeStatusListener {
        
        @Override
        protected String getProcessDefinitionKey() {
            return "supplier_change_process";
        }

        @Override
        protected ListenerConfig getListenerConfig() {
            ListenerConfig config = new ListenerConfig();
            config.setRelationField("supplier_id");
            // 不配置字段映射，直接同步同名字段
            return config;
        }
    }

    /**
     * 场景3：客户信息变更 - 仅记录历史
     */
    @Component
    public static class CustomerChangeListener extends BpmGenericFormChangeStatusListener {
        
        @Override
        protected String getProcessDefinitionKey() {
            return "customer_change_process";
        }

        @Override
        protected ListenerConfig getListenerConfig() {
            ListenerConfig config = new ListenerConfig();
            config.setRelationField("customer_id");
            config.setChangeRecordField("customerChangeHistory");
            config.setRecordOnly(true); // 仅记录模式
            
            // 只记录关键变更信息
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("old_level", "original_level");
            fieldMapping.put("new_level", "target_level");
            fieldMapping.put("change_reason", "reason");
            config.setFieldMapping(fieldMapping);
            
            return config;
        }
    }

    /**
     * 场景4：项目信息变更 - 混合模式
     */
    @Component
    public static class ProjectChangeListener extends BpmGenericFormChangeStatusListener {
        
        @Override
        protected String getProcessDefinitionKey() {
            return "project_change_process";
        }

        @Override
        protected ListenerConfig getListenerConfig() {
            ListenerConfig config = new ListenerConfig();
            config.setRelationField("project_id");
            config.setChangeRecordField("projectChangeRecord");
            
            // 部分字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("new_budget", "project_budget");
            fieldMapping.put("new_manager", "project_manager");
            fieldMapping.put("new_deadline", "project_deadline");
            config.setFieldMapping(fieldMapping);
            
            // 跳过敏感字段
            config.setSkipFields(Arrays.asList(
                "internal_comment", 
                "risk_assessment", 
                "confidential_info"
            ));
            
            return config;
        }
    }

    /**
     * 场景5：设备信息变更 - 最小配置
     */
    @Component
    public static class EquipmentChangeListener extends BpmGenericFormChangeStatusListener {
        
        @Override
        protected String getProcessDefinitionKey() {
            return "equipment_change_process";
        }

        @Override
        protected ListenerConfig getListenerConfig() {
            ListenerConfig config = new ListenerConfig();
            config.setRelationField("equipment_id");
            // 最简配置，直接同步所有字段
            return config;
        }
    }
}
