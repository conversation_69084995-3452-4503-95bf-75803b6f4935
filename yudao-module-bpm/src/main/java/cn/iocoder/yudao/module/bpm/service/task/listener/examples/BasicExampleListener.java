package cn.iocoder.yudao.module.bpm.service.task.listener.examples;

import cn.iocoder.yudao.module.bpm.service.task.listener.BpmGenericFormChangeStatusListener;
import org.springframework.stereotype.Component;

/**
 * 基础配置示例 - 最简单的使用方式
 * 
 * 适用场景：
 * - 简单的表单变更同步
 * - 字段名完全一致
 * - 不需要特殊处理
 * 
 * <AUTHOR>
 */
@Component
public class BasicExampleListener extends BpmGenericFormChangeStatusListener {

    @Override
    protected String getProcessDefinitionKey() {
        // 只处理指定的流程定义
        return "basic_change_process";
    }

    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // 基础配置：通过 target_id 字段关联目标流程实例
        config.setRelationField("target_id");
        
        return config;
    }
}
