package cn.iocoder.yudao.module.bpm.service.task.listener;

import org.springframework.stereotype.Component;

/**
 * 合同变更审批状态监听器
 * 当合同变更流程审批通过后，自动更新对应的合同台账数据
 * 
 * 使用示例：
 * 1. 在合同变更流程中，表单包含 contract_id 字段
 * 2. 当变更流程审批通过后，会自动查找 contract_id 对应的合同台账流程实例
 * 3. 将变更表单的数据同步到合同台账中
 * 
 * <AUTHOR>
 */
@Component
public class BpmContractChangeStatusListener extends BpmGenericFormChangeStatusListener {

    /**
     * 监听合同变更流程
     */
    @Override
    protected String getProcessDefinitionKey() {
        return "contract_change_process"; // 替换为实际的合同变更流程定义Key
    }

    /**
     * 通过 contract_id 字段关联合同台账
     */
    @Override
    protected String getRelationField() {
        return "contract_id";
    }
}
