package cn.iocoder.yudao.module.bpm.service.task.listener;

import org.springframework.stereotype.Component;

/**
 * 合同变更审批状态监听器（完整示例）
 * 当合同变更流程审批通过后，自动更新对应的合同台账数据
 *
 * 使用示例：
 * 1. 在合同变更流程中，表单包含 contract_id 字段
 * 2. 当变更流程审批通过后，会自动查找 contract_id 对应的合同台账流程实例
 * 3. 将变更表单的数据同步到合同台账中
 *
 * 这是使用新的 BpmGenericFormChangeStatusListener 的完整示例，
 * 展示了如何配置监听器来处理表单变更。
 *
 * <AUTHOR>
 */
@Component
public class BpmContractChangeStatusListener extends BpmGenericFormChangeStatusListener {

    /**
     * 监听合同变更流程
     */
    @Override
    protected String getProcessDefinitionKey() {
        return "contract_change_process"; // 替换为实际的合同变更流程定义Key
    }

    /**
     * 配置监听器参数
     */
    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();

        // 基础配置：通过 contract_id 字段关联合同台账
        config.setRelationField("contract_id");

        // 可选配置：变更记录字段（如果需要记录变更历史）
        config.setChangeRecordField("contractChangeRecord");

        // 可选配置：字段映射（如果变更表单字段名与台账字段名不同）
        // Map<String, String> fieldMapping = new HashMap<>();
        // fieldMapping.put("new_contract_amount", "contract_amount");
        // fieldMapping.put("new_contract_name", "contract_name");
        // config.setFieldMapping(fieldMapping);

        // 可选配置：跳过的字段列表（不需要同步到台账的字段）
        // List<String> skipFields = Arrays.asList("approval_comment", "temp_field");
        // config.setSkipFields(skipFields);

        // 可选配置：仅记录模式（只添加变更记录，不更新其他字段）
        // config.setRecordOnly(false);

        return config;
    }
}
